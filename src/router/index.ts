import { createRouter, createWebHashHistory } from 'vue-router'
import { createPermissionGuard } from './permission'
import { createMetaGuard } from './meta'
import { createWechatGuard } from './wechatGuard'
import { routes, handleHotUpdate } from 'vue-router/auto-routes'

export const history = createWebHashHistory()

export const router = createRouter({
  history,
  routes,
})

createPermissionGuard(router)
createMetaGuard(router)
createWechatGuard(router)

if (import.meta.hot) {
  handleHotUpdate(router)
}
