<script lang="ts" setup>
import type {
  PurpleAPIModel,
  V1LocationStudyCheckProjectIDUserIDGetResponseResult,
  V1ManageUserLearningsLearningDetailIDGetResponseResult,
  V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult,
} from '@/api/api.model'
import type { IconStatisticsProps } from '@/components/IconStatistics.vue'
import BasicUserInfo from '@/components/BasicUserInfo.vue'
import dayjs from 'dayjs'
import {
  V1EvaluateTaskPagePost,
  V1LocationHomeProjectSubscribeCancelPost,
  V1LocationHomeUserDataUserId,
  V1ManageTrainStudyRecordsPagePost,
  V1ManageUserLearningsPagePost,
  V1MobileHomePagePost,
} from '@/api/api.req'
import Footer from '@/components/layOut/footer.vue'
import QrScanner from '@/components/QrScanner/index.vue'
import { ExamStatusEnum, ExamStatusOptions, PaperTypeEnum } from '@/enums/exam'
import { LearningStatus, LearningStatusOptions } from '@/enums/learning'
import { TrainingStatus, TrainingStatusOptions } from '@/enums/training'
import { useAsyncData } from '@/hooks/useAsyncData'
import { useDynamicList } from '@/hooks/useDynamicList'
import { useLoading } from '@/hooks/useLoading'
import { currentUserInfo } from '@/store/sysUser'
import { getOptionLabel, toFixed } from '@/utils'
import { showToast } from 'vant'
import type { ICommonListItem } from '@/components/CommonList.vue'

const router = useRouter()
const basicInfoApi = useAsyncData(
  () =>
    V1LocationHomeUserDataUserId({
      userId: currentUserInfo.value.id!,
    }),
  {},
)

const basicInfoData = computed(() => {
  const data = basicInfoApi.data.value
  const secondsToHours = (s?: number) => toFixed(dayjs.duration(s || 0, 's').asHours(), 2)

  const configs: IconStatisticsProps[] = [
    {
      icon: 'svg/item/Desktop',
      title: '在线学习',
      value: secondsToHours(+data.learningDuration!),
      unit: 'h',
    },
    {
      icon: 'svg/item/DeploymentUnit',
      title: '技能训练',
      value: secondsToHours(+data.trainDuration!),
      unit: 'h',
    },
    {
      icon: 'svg/item/Audit',
      title: '考试次数',
      value: data.examNum || 0,
      unit: '次',
    },
    {
      icon: 'svg/item/Crown',
      title: '被评估',
      // tag: `${data.evaluateNum || 0} 次`,
      value: data.evaluateNum || 0,
      unit: '次',
    },
  ]

  return configs
})

const todoTrainingState = useDynamicList({
  api: async (params) => {
    const resp = await V1ManageTrainStudyRecordsPagePost({
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        statusList: [TrainingStatus.TO_BE],
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => {
        return {
          ...item,
          id: item.id!,
          name: item.projectName!,
          tag: getOptionLabel(TrainingStatusOptions, item.status)!,
          linkText: item.subscribeInfo?.id ? '取消订阅' : undefined,
          showArrow: false,
        } satisfies ICommonListItem
      }),
    }
  },
})
const todoBeCheckState = useDynamicList({
  api: async (params) => {
    const resp = await V1ManageTrainStudyRecordsPagePost({
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        statusList: ['TO_BE_CHECK'],
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => {
        return {
          ...item,
          id: item.id!,
          name: item.projectName!,
          tag: '待考核',
        }
      }),
    }
  },
})
const todoLearningState = useDynamicList({
  api: async (params) => {
    const resp = await V1ManageUserLearningsPagePost({
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        statusList: [LearningStatus.WAITING, LearningStatus.LEARNING],
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => {
        return {
          ...item,
          id: item.id!,
          name: item.name!,
          tag: getOptionLabel(LearningStatusOptions, item.status)!,
          linkText: '去学习',
        }
      }),
    }
  },
})
const todoExamState = useDynamicList({
  api: async (params) => {
    const resp = await V1MobileHomePagePost({
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        type: PaperTypeEnum.SKILL_INSPECTION,
        statusList: [ExamStatusEnum.TO_BE],
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => {
        return {
          ...item,
          id: item.id!,
          name: item.paperName!,
          tag: getOptionLabel(ExamStatusOptions, item.status)!,
          linkText: '去考试',
        }
      }),
    }
  },
})
const todoEvaluateState = useDynamicList({
  api: async (params) => {
    const resp = await V1EvaluateTaskPagePost({
      ...params,
      data: {
        reviewerUserId: currentUserInfo.value.id!,
        status: 0,
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => {
        return {
          ...item,
          id: item.id!,
          name: item.evaluateName!,
          tag: '待评审',
          linkText: '去评审',
        }
      }),
    }
  },
})

const fetchData = useLoading(_fetchData)

fetchData()

function _fetchData() {
  basicInfoApi.load()
  loadListData()
}

function loadListData() {
  todoTrainingState.reset()
  todoTrainingState.load()

  todoBeCheckState.reset()
  todoBeCheckState.load()

  todoLearningState.reset()
  todoLearningState.load()

  todoExamState.reset()
  todoExamState.load()

  todoEvaluateState.reset()
  todoEvaluateState.load()
}

async function onClickBeTrainingItem(item: V1LocationStudyCheckProjectIDUserIDGetResponseResult) {
  if (!item.subscribeInfo?.id) {
    return
  }

  await V1LocationHomeProjectSubscribeCancelPost({
    userId: currentUserInfo.value.id!,
    projectIdList: [item.projectId!],
  })

  showToast(`已取消订阅`)
  todoTrainingState.reset()
  todoTrainingState.load()
}

function onClickBeCheckItem(item: V1LocationStudyCheckProjectIDUserIDGetResponseResult) {
  // 根据项目类型跳转到对应的详情页面
  let detailPath = '/training/detail'

  // projectType: 1-训练, 2-考核, 3-比赛
  switch (item.projectType) {
    case 1:
      detailPath = '/training/report-detail'
      break
    case 2:
      detailPath = '/training/exam-detail'
      break
    case 3:
      detailPath = '/training/competition-detail'
      break
    default:
      detailPath = '/training/detail'
  }

  router.push({
    path: detailPath,
    query: {
      id: item.id,
      userId: currentUserInfo.value.id,
    },
  })
}

function onClickLearningItem(item: V1ManageUserLearningsLearningDetailIDGetResponseResult) {
  router.push({
    path: '/learning/detail',
    query: { id: item.id },
  })
}

function onClickExamItem(item: V1MobileHomeExamRecordHistoryPaperIDUserIDGetResponseResult) {
  router.push({
    path: '/exam/detail',
    query: { id: item.id },
  })
}

function onClickEvaluateItem(item: PurpleAPIModel) {
  router.push({
    path: '/evaluate/detail',
    query: { id: item.id },
  })
}
</script>

<template>
  <VanNavBar title="首页" />
  <div class="page-content page-content-padding">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <div class="flex flex-col gap-4">
        <BasicUserInfo
          :user-info="currentUserInfo"
          :org-list="basicInfoApi.data.value.orgList"
          :level="basicInfoApi.data.value.level"
        >
          <div class="flex justify-between px-4 py-4">
            <IconStatistics v-for="(info, index) in basicInfoData" :key="index" v-bind="info" />
          </div>
        </BasicUserInfo>

        <CardBox>
          <div class="title">待办事项</div>
          <CommonList
            :list-state="todoTrainingState"
            finish-text=""
            @click-item="onClickBeTrainingItem"
          />
          <CommonList
            :list-state="todoBeCheckState"
            finish-text=""
            @click-item="onClickBeCheckItem"
          />
          <CommonList
            :list-state="todoLearningState"
            finish-text=""
            @click-item="onClickLearningItem"
          />
          <CommonList :list-state="todoExamState" finish-text="" @click-item="onClickExamItem" />
          <CommonList :list-state="todoEvaluateState" @click-item="onClickEvaluateItem" />
        </CardBox>
      </div>
    </VanPullRefresh>
  </div>
  <Footer />
</template>

<style lang="less" scoped>
.title {
  color: var(--text-icon-font-gy-190, rgba(0, 0, 0, 0.9));
  font-weight: bold;
  margin-bottom: 8px;
}
</style>
