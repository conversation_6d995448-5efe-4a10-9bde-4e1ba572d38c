<script lang="ts" setup>
import { PREVIEW_BASE_URL } from '@/env'

interface Props {
  fileUrl: string
  fileName?: string
  fileType?: string
  fileSize?: number
  previewBaseUrl?: string
  moduleType?: number
  waterMark?: string
  createTime?: string
}

const props = withDefaults(defineProps<Props>(), {
  fileName: '',
  fileType: '',
  previewBaseUrl: PREVIEW_BASE_URL || 'http://kkvf-big-excel-view.caas-cloud-test.geega.com',
  moduleType: 9,
  waterMark: '智工育匠',
  createTime: '',
})

const isLoading = ref(true)
const hasError = ref(false)
const iframeRef = ref<HTMLIFrameElement>()

// 格式化创建时间
function formatCreateTime(createTime?: string): string {
  if (!createTime) {
    return new Date().toLocaleDateString()
  }

  try {
    // 尝试解析时间字符串
    const date = new Date(createTime)
    if (Number.isNaN(date.getTime())) {
      // 如果解析失败，返回当前时间
      return new Date().toLocaleDateString()
    }
    return date.toLocaleDateString()
  }
  catch {
    // 如果出错，返回当前时间
    return new Date().toLocaleDateString()
  }
}

// 构建预览URL - 参考PC端的实现方式
const previewUrl = computed(() => {
  if (!props.fileUrl)
    return ''

  try {
    // 构建参数对象，参考PC端的格式
    const params = {
      url: props.fileUrl,
      moduleType: 9,
      down: false,
      waterMark: props.waterMark,
    }

    // 使用PC端相同的编码方式
    const encodedParams = btoa(
      unescape(
        encodeURIComponent(
          JSON.stringify(params),
        ),
      ),
    )

    return `${props.previewBaseUrl}/onlinePreview?params=${encodeURIComponent(encodedParams)}`
  }
  catch {
    return ''
  }
})

// 获取文件扩展名
const fileExtension = computed(() => {
  if (props.fileType)
    return props.fileType.toLowerCase()

  const fileName = props.fileName || props.fileUrl
  const lastDot = fileName.lastIndexOf('.')
  return lastDot > -1 ? fileName.substring(lastDot + 1).toLowerCase() : ''
})

// 获取文件类型显示名称
const fileTypeDisplay = computed(() => {
  const ext = fileExtension.value.toUpperCase()

  const typeMap: Record<string, string> = {
    // 具体文件扩展名
    PDF: 'PDF文档',
    DOC: 'Word文档',
    DOCX: 'Word文档',
    XLS: 'Excel表格',
    XLSX: 'Excel表格',
    PPT: 'PowerPoint演示',
    PPTX: 'PowerPoint演示',
    TXT: '文本文件',
    MP4: '视频',
    AVI: '视频',

    // 通用文件类型（后端可能返回的类型）
    WORD: 'Word文档',
    EXCEL: 'Excel表格',
    POWERPOINT: 'PowerPoint演示',
    VIDEO: '视频',
  }

  return typeMap[ext] || ext || '文件'
})

// 判断是否支持预览 - 根据PC端配置限制支持的文件类型
const isPreviewSupported = computed(() => {
  const ext = fileExtension.value

  // 支持的具体文件扩展名
  const supportedExtensions = [
    'docx',
    'doc',
    'xlsx',
    'xls',
    'ppt',
    'pptx',
    'pdf',
    'mp4',
  ]

  // 支持的通用文件类型（后端可能返回的类型）
  const supportedGenericTypes = [
    'word', // Word文档
    'excel', // Excel表格
    'powerpoint', // PowerPoint演示文稿
    'pdf', // PDF文档
    'video', // 视频文件
  ]

  return supportedExtensions.includes(ext) || supportedGenericTypes.includes(ext)
})

// 检查文件大小是否超过限制（1GB = 1024 * 1024 * 1024 bytes）
const isFileSizeValid = computed(() => {
  if (!props.fileSize)
    return true // 如果没有文件大小信息，默认允许
  const maxSize = 1024 * 1024 * 1024 // 1GB
  return props.fileSize <= maxSize
})

// 综合判断是否可以预览
const canPreview = computed(() => {
  return isPreviewSupported.value && isFileSizeValid.value
})

function onIframeLoad() {
  isLoading.value = false
}

function onIframeError() {
  isLoading.value = false
  hasError.value = true
}

function retryPreview() {
  hasError.value = false
  isLoading.value = true

  // 重新加载iframe
  if (iframeRef.value) {
    iframeRef.value.src = previewUrl.value
  }
}

function openInNewTab() {
  window.open(previewUrl.value, '_blank')
}
</script>

<template>
  <div class="file-preview">
    <!-- 文件信息头部 -->
    <div class="file-header">
      <div class="file-info">
        <div class="file-details">
          <h3 class="file-name">
            {{ fileName || '未知文件' }}
          </h3>
          <div class="file-meta">
            <div class="left-info">
              <span class="file-type">{{ fileTypeDisplay }}</span>
              <span class="update-time">更新时间: {{ formatCreateTime(createTime) }}</span>
            </div>
            <div class="file-actions">
              <VanButton
                v-if="canPreview"
                size="small"
                icon="enlarge"
                @click="openInNewTab"
              >
                新窗口打开
              </VanButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览内容 -->
    <div class="preview-container">
      <!-- 不支持预览的文件类型 -->
      <div v-if="!isPreviewSupported" class="preview-unsupported">
        <VanIcon name="warning-o" />
        <p>此文件类型不支持在线预览</p>
        <p class="support-tip">
          支持格式：Word文档、Excel表格、PowerPoint演示文稿、PDF文档、视频文件
        </p>
      </div>

      <!-- 文件大小超限 -->
      <div v-else-if="!isFileSizeValid" class="preview-unsupported">
        <VanIcon name="warning-o" />
        <p>文件大小超过限制</p>
        <p class="support-tip">
          文件大小不能超过1GB
        </p>
      </div>

      <!-- 支持预览的文件 -->
      <template v-else>
        <!-- 加载状态 -->
        <div v-if="isLoading && !hasError" class="preview-loading">
          <VanLoading type="spinner" />
          <span>文件预览加载中...</span>
        </div>

        <!-- 错误状态 -->
        <div v-if="hasError" class="preview-error">
          <VanIcon name="warning-o" />
          <p>文件预览加载失败</p>
          <div class="error-actions">
            <VanButton size="small" @click="retryPreview">
              重试
            </VanButton>
          </div>
        </div>

        <!-- 预览iframe -->
        <iframe
          v-show="!hasError && previewUrl"
          ref="iframeRef"
          :src="previewUrl"
          class="preview-iframe"
          frameborder="0"
          @load="onIframeLoad"
          @error="onIframeError"
        />
      </template>
    </div>
  </div>
</template>

<style lang="less" scoped>
.file-preview {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.file-header {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.file-info {
  width: 100%;

  .file-details {
    width: 100%;

    .file-name {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
      word-break: break-all;
    }

    .file-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
      font-size: 12px;
      color: #666;

      .left-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
      }

      .file-type {
        background: #e8f4ff;
        color: #1989fa;
        padding: 2px 6px;
        border-radius: 4px;
        flex-shrink: 0;
      }

      .update-time {
        flex-shrink: 0;
      }

      .file-actions {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
      }
    }
  }
}

.preview-container {
  position: relative;
  width: 100%;
  min-height: 400px;
  background: #f8f9fa;
}

.preview-loading,
.preview-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;

  .van-icon {
    font-size: 48px;
    color: #ddd;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.preview-unsupported {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 16px;
  color: #666;

  .van-icon {
    font-size: 48px;
    color: #ddd;
  }

  p {
    margin: 0;
    font-size: 14px;
  }

  .support-tip {
    font-size: 12px;
    color: #999;
    text-align: center;
    line-height: 1.4;
  }
}

.preview-error .van-icon {
  color: #ff4444;
}

.error-actions {
  display: flex;
  gap: 12px;
}

.preview-iframe {
  width: 100%;
  height: 600px;
  border: none;
  background: #fff;
}

@media (max-width: 768px) {
  .file-info .file-details .file-meta {
    gap: 8px;

    .left-info {
      gap: 4px;
      width: 100%;
    }
  }

  .preview-iframe {
    height: 400px;
  }
}
</style>
