/**
 * 微信SDK初始化插件
 */
import { defineVuePlugin } from '@/utils/helper'
import { useWechatStore } from '@/store/wechat'
import { isWechatBrowser } from '@/utils/wechat'

export default defineVuePlugin((app) => {
  // 在应用启动时初始化微信SDK
  if (typeof window !== 'undefined' && isWechatBrowser()) {
    // 延迟初始化，确保DOM已加载
    setTimeout(() => {
      const wechatStore = useWechatStore()
      wechatStore.initializeWechatSDK().catch(error => {
        console.warn('微信SDK初始化失败:', error)
      })
    }, 100)
  }
})
